#!/usr/bin/env python3
"""
调试Agent响应格式
查看Agent调用MCP工具返回的具体数据格式
"""

import asyncio
import os
import sys
import json
from pathlib import Path

# 设置Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入组件
from mcp_use import MCPClient
from src.scanner.cluster_scanner import ClusterScanner
from src.cache import CacheManager
from src.mcp_tools import MCPToolLoader


async def debug_agent_response():
    """调试Agent响应格式"""
    print("=" * 60)
    print("🔍 调试Agent响应格式")
    print("=" * 60)
    
    try:
        # 创建组件
        config = {
            "mcpServers": {
                os.getenv("MCP_SERVER_NAME", "k8s"): {
                    "type": os.getenv("MCP_SERVER_TYPE", "sse"),
                    "url": os.getenv("MCP_SERVER_URL", "")
                }
            }
        }
        mcp_client = MCPClient.from_dict(config)
        cache_manager = CacheManager()
        tool_loader = MCPToolLoader(cache_manager)
        
        cluster_scanner = ClusterScanner(
            mcp_client=mcp_client,
            tool_loader=tool_loader,
            timeout=60,
            max_retries=2
        )
        
        print("✅ 组件创建成功")
        
        # 测试各种MCP工具调用
        tools_to_test = [
            ('k8s_get_cluster_info', {}),
            ('k8s_list_namespaces', {}),
            ('k8s_list_nodes', {}),
            ('k8s_list_pods', {'namespace': 'default'}),
        ]
        
        for tool_name, params in tools_to_test:
            print(f"\n🧪 测试工具: {tool_name}")
            print(f"   参数: {params}")
            
            try:
                result = await cluster_scanner._call_mcp_tool(tool_name, params)
                
                print(f"✅ 调用成功")
                print(f"   结果类型: {type(result)}")
                print(f"   结果长度: {len(str(result))}")
                
                # 显示结果内容（前200字符）
                result_str = str(result)
                if len(result_str) > 200:
                    preview = result_str[:200] + "..."
                else:
                    preview = result_str
                
                print(f"   结果预览: {preview}")
                
                # 尝试解析为JSON
                if isinstance(result, str):
                    try:
                        json_data = json.loads(result)
                        print(f"   ✅ 可解析为JSON")
                        print(f"   JSON类型: {type(json_data)}")
                        if isinstance(json_data, dict):
                            print(f"   JSON键: {list(json_data.keys())}")
                        elif isinstance(json_data, list):
                            print(f"   JSON列表长度: {len(json_data)}")
                            if json_data and isinstance(json_data[0], dict):
                                print(f"   第一项键: {list(json_data[0].keys())}")
                    except json.JSONDecodeError:
                        print(f"   ❌ 不是有效的JSON格式")
                        # 检查是否是YAML或其他格式
                        if result.strip().startswith('{') or result.strip().startswith('['):
                            print(f"   💡 看起来像JSON但解析失败")
                        elif 'apiVersion:' in result or 'kind:' in result:
                            print(f"   💡 看起来像YAML格式")
                        else:
                            print(f"   💡 可能是纯文本描述")
                
                print(f"   " + "-" * 40)
                
            except Exception as e:
                print(f"❌ 调用失败: {e}")
                print(f"   错误类型: {type(e)}")
        
        print("\n" + "=" * 60)
        print("🎯 调试总结:")
        print("   1. 检查Agent返回的数据格式")
        print("   2. 确定是否需要JSON解析")
        print("   3. 了解数据结构以修复ResourceParser")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 启动Agent响应调试程序")
    asyncio.run(debug_agent_response())


if __name__ == '__main__':
    main()
