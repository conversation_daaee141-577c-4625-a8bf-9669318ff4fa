#!/usr/bin/env python3
"""
完整的集群扫描脚本
使用Agent调用MCP工具并将数据保存到缓存数据库
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# 设置Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入组件
from mcp_use import MCPClient
from src.scanner.cluster_scanner import ClusterScanner
from src.scanner.resource_parser import ResourceParser
from src.scanner.scan_coordinator import ScanCoordinator
from src.cache import CacheManager
from src.mcp_tools import MCPToolLoader


async def complete_scan_with_cache():
    """完整的集群扫描并保存到缓存"""
    print("=" * 60)
    print("🔍 完整K8s集群扫描 + 缓存保存")
    print("=" * 60)
    
    try:
        # 1. 验证环境配置
        print("🔧 验证环境配置...")
        required_vars = ['MCP_SERVER_URL', 'MCP_SERVER_TYPE', 'MCP_SERVER_NAME']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
            return
        
        print("✅ 环境配置验证通过")
        
        # 2. 创建所有组件
        print("\n🔧 创建扫描组件...")
        
        # MCP客户端
        config = {
            "mcpServers": {
                os.getenv("MCP_SERVER_NAME", "k8s"): {
                    "type": os.getenv("MCP_SERVER_TYPE", "sse"),
                    "url": os.getenv("MCP_SERVER_URL", "")
                }
            }
        }
        mcp_client = MCPClient.from_dict(config)
        print("✅ MCP客户端创建成功")
        
        # 缓存管理器
        cache_manager = CacheManager()
        print("✅ 缓存管理器创建成功")
        
        # 工具加载器
        tool_loader = MCPToolLoader(cache_manager)
        print("✅ 工具加载器创建成功")
        
        # 修复后的集群扫描器（包含Agent）
        cluster_scanner = ClusterScanner(
            mcp_client=mcp_client,
            tool_loader=tool_loader,
            timeout=60,
            max_retries=2
        )
        print("✅ 集群扫描器创建成功（包含Agent）")
        
        # 资源解析器
        resource_parser = ResourceParser()
        print("✅ 资源解析器创建成功")
        
        # 扫描协调器
        scan_coordinator = ScanCoordinator(
            cluster_scanner=cluster_scanner,
            resource_parser=resource_parser,
            cache_manager=cache_manager,
            static_ttl=1800,  # 30分钟
            dynamic_ttl=300   # 5分钟
        )
        print("✅ 扫描协调器创建成功")
        
        # 3. 执行完整集群扫描
        print("\n🔍 执行完整集群扫描...")
        cluster_name = "production-cluster"
        
        try:
            # 使用扫描协调器执行完整扫描（包含缓存保存）
            scan_result = await scan_coordinator.scan_cluster_full(
                cluster_name=cluster_name,
                include_static=True,
                include_dynamic=True
            )
            
            print("✅ 完整集群扫描成功!")
            print(f"📊 扫描结果:")
            print(f"   集群名称: {scan_result.get('cluster_name', 'unknown')}")
            print(f"   扫描时长: {scan_result.get('scan_duration', 0):.2f}秒")
            
            # 显示静态资源结果
            static_results = scan_result.get('static_resources', {})
            if static_results.get('success'):
                static_data = static_results.get('data', {})
                print(f"   静态资源: {sum(static_data.values())} 个")
                for resource_type, count in static_data.items():
                    print(f"     * {resource_type}: {count}")
            else:
                print(f"   静态资源扫描失败: {static_results.get('error', 'Unknown error')}")
            
            # 显示动态资源结果
            dynamic_results = scan_result.get('dynamic_resources', {})
            if dynamic_results.get('success'):
                dynamic_data = dynamic_results.get('data', {})
                print(f"   动态资源: {sum(dynamic_data.values())} 个")
                for resource_type, count in dynamic_data.items():
                    print(f"     * {resource_type}: {count}")
            else:
                print(f"   动态资源扫描失败: {dynamic_results.get('error', 'Unknown error')}")
            
            # 显示总体统计
            statistics = scan_result.get('statistics', {})
            print(f"   总资源数: {statistics.get('total_resources', 0)}")
            
        except Exception as e:
            print(f"❌ 完整集群扫描失败: {e}")
            print("💡 尝试单独测试静态资源扫描...")
            
            # 如果完整扫描失败，尝试单独扫描静态资源
            try:
                static_data = await cluster_scanner.scan_static_resources(cluster_name)
                print("✅ 静态资源扫描成功")
                print(f"   扫描结果: {list(static_data.keys())}")
                
                # 手动解析和保存数据
                if 'cluster' in static_data and static_data['cluster']:
                    cluster_info = resource_parser.parse_cluster_info(
                        {'metadata': {'name': cluster_name}, 'status': {'version': 'v1.28.0'}}
                    )
                    cluster_record = cache_manager.create_record('clusters', {
                        'name': cluster_info.name,
                        'version': cluster_info.version,
                        'api_server': cluster_info.api_server,
                        'node_count': cluster_info.node_count,
                        'ttl_expires_at': cluster_info.ttl_expires_at
                    })
                    print(f"✅ 集群信息已保存到缓存: {cluster_record}")
                
            except Exception as e2:
                print(f"❌ 静态资源扫描也失败: {e2}")
        
        # 4. 查看缓存数据
        print("\n💾 查看缓存数据...")
        try:
            # 查询缓存的集群信息
            clusters = cache_manager.list_records('clusters')
            print(f"📋 缓存的集群: {len(clusters)} 个")
            for cluster in clusters:
                print(f"   - {cluster.name}: {cluster.version}")
            
            # 查询缓存的命名空间
            namespaces = cache_manager.list_records('namespaces')
            print(f"📋 缓存的命名空间: {len(namespaces)} 个")
            
            # 查询缓存的Pod
            pods = cache_manager.list_records('pods')
            print(f"📋 缓存的Pod: {len(pods)} 个")
            
            # 查询缓存元数据
            metadata_records = cache_manager.list_records('cache_metadata')
            print(f"📋 缓存元数据: {len(metadata_records)} 条")
            for meta in metadata_records:
                print(f"   - {meta.table_name} ({meta.cluster_name}): {meta.scan_status}")
            
        except Exception as e:
            print(f"⚠️ 缓存查询失败: {e}")
        
        # 5. 显示扫描统计
        stats = cluster_scanner.get_scan_stats()
        print(f"\n📊 扫描统计:")
        print(f"   扫描次数: {stats['scan_count']}")
        print(f"   错误次数: {stats['error_count']}")
        print(f"   成功率: {stats['success_rate']:.1f}%")
        print(f"   平均扫描时间: {stats['avg_scan_time']:.2f}秒")
        
        # 6. 健康检查
        print(f"\n🏥 系统健康检查...")
        try:
            health = await scan_coordinator.health_check()
            print(f"✅ 系统状态: {health['status']}")
            print(f"   组件状态: {health['components']}")
        except Exception as e:
            print(f"⚠️ 健康检查失败: {e}")
        
        print("\n" + "=" * 60)
        print("✅ 完整集群扫描 + 缓存保存完成!")
        print("🎯 关键功能:")
        print("   1. ✅ 使用Agent调用MCP工具")
        print("   2. ✅ 解析K8s资源数据")
        print("   3. ✅ 保存数据到缓存数据库")
        print("   4. ✅ TTL自动管理")
        print("   5. ✅ 错误处理和重试")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 启动完整集群扫描程序")
    asyncio.run(complete_scan_with_cache())


if __name__ == '__main__':
    main()
