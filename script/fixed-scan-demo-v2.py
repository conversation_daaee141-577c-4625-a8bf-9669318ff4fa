#!/usr/bin/env python3
"""
修复的集群扫描演示 - 确保扫描系统正常工作
使用正确的MCP工具名称和参数
"""

import asyncio
import os
import sys
import json
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from mcp_use import MCPClient

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from src.cache.cache_manager import CacheManager
from src.llm_config import create_llm
from mcp_use import MCPAgent


class FixedClusterScanner:
    """修复的集群扫描器 - 使用正确的MCP工具"""
    
    def __init__(self, mcp_client):
        self.mcp_client = mcp_client
        # 创建MCP Agent使用正确的方式
        self.agent = MCPAgent(
            llm=create_llm(),
            client=mcp_client,
            max_steps=10
        )
        self.scan_stats = {
            'scan_count': 0,
            'success_count': 0,
            'error_count': 0
        }
    
    async def scan_cluster_info(self, cluster_name: str = "default-cluster"):
        """扫描集群信息"""
        try:
            print(f"🔍 扫描集群信息: {cluster_name}")
            
            # 使用正确的工具名称和参数
            result = await self.agent.run(
                f"使用 GET_CLUSTER_INFO 工具获取集群 {cluster_name} 的信息",
                max_steps=3
            )
            
            print(f"✅ 集群信息获取成功")
            self.scan_stats['success_count'] += 1
            return result
            
        except Exception as e:
            print(f"❌ 集群信息扫描失败: {e}")
            self.scan_stats['error_count'] += 1
            return None
        finally:
            self.scan_stats['scan_count'] += 1
    
    async def scan_namespaces(self, cluster_name: str = "default-cluster"):
        """扫描命名空间"""
        try:
            print(f"🔍 扫描命名空间: {cluster_name}")
            
            result = await self.agent.run(
                f"使用 LIST_NAMESPACES 工具列出集群 {cluster_name} 的所有命名空间",
                max_steps=3
            )
            
            print(f"✅ 命名空间获取成功")
            self.scan_stats['success_count'] += 1
            return result
            
        except Exception as e:
            print(f"❌ 命名空间扫描失败: {e}")
            self.scan_stats['error_count'] += 1
            return None
        finally:
            self.scan_stats['scan_count'] += 1
    
    async def scan_nodes(self, cluster_name: str = "default-cluster"):
        """扫描节点"""
        try:
            print(f"🔍 扫描节点: {cluster_name}")
            
            result = await self.agent.run(
                f"使用 LIST_NODES 工具列出集群 {cluster_name} 的所有节点",
                max_steps=3
            )
            
            print(f"✅ 节点获取成功")
            self.scan_stats['success_count'] += 1
            return result
            
        except Exception as e:
            print(f"❌ 节点扫描失败: {e}")
            self.scan_stats['error_count'] += 1
            return None
        finally:
            self.scan_stats['scan_count'] += 1
    
    async def scan_pods(self, cluster_name: str = "default-cluster", namespace: str = None):
        """扫描Pod"""
        try:
            print(f"🔍 扫描Pod: {cluster_name}" + (f" (命名空间: {namespace})" if namespace else ""))
            
            if namespace:
                query = f"使用 LIST_CORE_RESOURCES 工具列出集群 {cluster_name} 命名空间 {namespace} 中的所有Pod，参数：apiVersion=v1, kind=Pod"
            else:
                query = f"使用 LIST_CORE_RESOURCES 工具列出集群 {cluster_name} 中的所有Pod，参数：apiVersion=v1, kind=Pod"
            
            result = await self.agent.run(query, max_steps=3)
            
            print(f"✅ Pod获取成功")
            self.scan_stats['success_count'] += 1
            return result
            
        except Exception as e:
            print(f"❌ Pod扫描失败: {e}")
            self.scan_stats['error_count'] += 1
            return None
        finally:
            self.scan_stats['scan_count'] += 1
    
    async def scan_services(self, cluster_name: str = "default-cluster", namespace: str = None):
        """扫描服务"""
        try:
            print(f"🔍 扫描服务: {cluster_name}" + (f" (命名空间: {namespace})" if namespace else ""))
            
            if namespace:
                query = f"使用 LIST_CORE_RESOURCES 工具列出集群 {cluster_name} 命名空间 {namespace} 中的所有Service，参数：apiVersion=v1, kind=Service"
            else:
                query = f"使用 LIST_CORE_RESOURCES 工具列出集群 {cluster_name} 中的所有Service，参数：apiVersion=v1, kind=Service"
            
            result = await self.agent.run(query, max_steps=3)
            
            print(f"✅ 服务获取成功")
            self.scan_stats['success_count'] += 1
            return result
            
        except Exception as e:
            print(f"❌ 服务扫描失败: {e}")
            self.scan_stats['error_count'] += 1
            return None
        finally:
            self.scan_stats['scan_count'] += 1


async def demo_fixed_scanning():
    """演示修复的扫描功能"""
    print("=" * 60)
    print("🔧 修复的K8s集群扫描演示")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    try:
        # 1. 初始化组件
        print("🔧 初始化组件...")
        
        # 创建MCP客户端
        config = {
            "mcpServers": {
                os.getenv("MCP_SERVER_NAME", "k8s"): {
                    "type": os.getenv("MCP_SERVER_TYPE", "sse"),
                    "url": os.getenv("MCP_SERVER_URL", "")
                }
            }
        }
        mcp_client = MCPClient.from_dict(config)
        print("✅ MCP客户端创建成功")
        
        # 创建缓存管理器
        cache_manager = CacheManager()
        print("✅ 缓存管理器创建成功")
        
        # 创建修复的扫描器
        scanner = FixedClusterScanner(mcp_client)
        print("✅ 扫描器创建成功")
        
        # 2. 执行扫描测试
        print("\n🔍 执行扫描测试...")
        cluster_name = "default-cluster"
        
        # 扫描静态资源
        print("\n📊 扫描静态资源:")
        cluster_info = await scanner.scan_cluster_info(cluster_name)
        namespaces_info = await scanner.scan_namespaces(cluster_name)
        nodes_info = await scanner.scan_nodes(cluster_name)
        
        # 扫描动态资源
        print("\n📦 扫描动态资源:")
        pods_info = await scanner.scan_pods(cluster_name)
        services_info = await scanner.scan_services(cluster_name)
        
        # 3. 处理扫描结果并存储到缓存
        print("\n💾 处理扫描结果...")
        
        scan_results = {
            'cluster_info': cluster_info,
            'namespaces': namespaces_info,
            'nodes': nodes_info,
            'pods': pods_info,
            'services': services_info
        }
        
        # 简单的结果统计
        successful_scans = sum(1 for result in scan_results.values() if result is not None)
        total_scans = len(scan_results)
        
        print(f"✅ 扫描完成: {successful_scans}/{total_scans} 成功")
        
        # 4. 显示扫描统计
        print("\n📈 扫描统计:")
        stats = scanner.scan_stats
        success_rate = (stats['success_count'] / stats['scan_count'] * 100) if stats['scan_count'] > 0 else 0
        print(f"   - 总扫描次数: {stats['scan_count']}")
        print(f"   - 成功次数: {stats['success_count']}")
        print(f"   - 失败次数: {stats['error_count']}")
        print(f"   - 成功率: {success_rate:.1f}%")
        
        # 5. 尝试存储一些测试数据到缓存
        print("\n💾 测试缓存存储...")
        try:
            # 创建测试集群记录
            test_cluster = {
                'name': cluster_name,
                'version': 'v1.28.0',
                'api_server': 'https://test-cluster.example.com:6443',
                'node_count': 3,
                'status': 'Ready'
            }
            
            # 存储到缓存（这里需要实现具体的存储逻辑）
            print(f"📝 创建测试集群记录: {test_cluster['name']}")
            
            # 查询缓存数据
            clusters = cache_manager.list_records('clusters')
            print(f"📋 缓存的集群数量: {len(clusters)}")
            
        except Exception as e:
            print(f"⚠️ 缓存测试失败: {e}")
        
        print("\n" + "=" * 60)
        print("✅ 修复的扫描演示完成!")
        print("💡 如果看到成功的扫描结果，说明MCP工具连接正常")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("🚀 启动修复的集群扫描演示")
    asyncio.run(demo_fixed_scanning())
