#!/usr/bin/env python3
"""
简单的缓存测试 - 验证扫描系统的缓存功能
不依赖MCP服务器连接，直接测试缓存存储和查询
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from src.cache.cache_manager import CacheManager
from src.cache.models import ClusterInfo, NamespaceInfo, NodeInfo, PodInfo, ServiceInfo


async def test_cache_functionality():
    """测试缓存功能"""
    print("=" * 60)
    print("🧪 缓存功能测试")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    try:
        # 1. 初始化缓存管理器
        print("🔧 初始化缓存管理器...")
        cache_manager = CacheManager()
        print("✅ 缓存管理器创建成功")
        
        # 2. 清理现有数据
        print("\n🧹 清理现有测试数据...")
        try:
            # 删除测试集群的所有数据
            test_cluster = "test-cluster"
            
            # 清理各个表的数据
            cache_manager.database.execute("DELETE FROM pods WHERE cluster_name = ?", (test_cluster,))
            cache_manager.database.execute("DELETE FROM services WHERE cluster_name = ?", (test_cluster,))
            cache_manager.database.execute("DELETE FROM nodes WHERE cluster_name = ?", (test_cluster,))
            cache_manager.database.execute("DELETE FROM namespaces WHERE cluster_name = ?", (test_cluster,))
            cache_manager.database.execute("DELETE FROM clusters WHERE name = ?", (test_cluster,))
            cache_manager.database.execute("DELETE FROM cache_metadata WHERE cluster_name = ?", (test_cluster,))
            cache_manager.database.commit()
            
            print("✅ 测试数据清理完成")
        except Exception as e:
            print(f"⚠️ 数据清理失败: {e}")
        
        # 3. 创建测试数据
        print("\n📝 创建测试数据...")
        
        # 创建集群记录
        cluster_record = ClusterInfo(
            name="test-cluster",
            version="v1.28.0",
            api_server="https://test-cluster.example.com:6443",
            node_count=3,
            ttl_expires_at=datetime.utcnow() + timedelta(minutes=30)
        )
        
        # 创建命名空间记录
        namespace_records = [
            NamespaceInfo(
                cluster_name="test-cluster",
                name="default",
                status="Active",
                labels={"env": "production"},
                annotations={"created-by": "system"},
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=30)
            ),
            NamespaceInfo(
                cluster_name="test-cluster",
                name="kube-system",
                status="Active",
                labels={"env": "system"},
                annotations={"created-by": "kubernetes"},
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=30)
            )
        ]
        
        # 创建节点记录
        node_records = [
            NodeInfo(
                cluster_name="test-cluster",
                name="master-node-1",
                status="Ready",
                roles=["master", "control-plane"],
                capacity={"cpu": "4", "memory": "8Gi", "storage": "100Gi"},
                allocatable={"cpu": "3.8", "memory": "7.5Gi", "storage": "95Gi"},
                labels={"node-role.kubernetes.io/master": "", "kubernetes.io/os": "linux"},
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=30)
            ),
            NodeInfo(
                cluster_name="test-cluster",
                name="worker-node-1",
                status="Ready",
                roles=["worker"],
                capacity={"cpu": "8", "memory": "16Gi", "storage": "200Gi"},
                allocatable={"cpu": "7.8", "memory": "15.5Gi", "storage": "190Gi"},
                labels={"node-role.kubernetes.io/worker": "", "kubernetes.io/os": "linux"},
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=30)
            )
        ]
        
        # 创建Pod记录
        pod_records = [
            PodInfo(
                cluster_name="test-cluster",
                namespace="default",
                name="web-app-123",
                status="Running",
                phase="Running",
                node_name="worker-node-1",
                labels={"app": "web", "version": "v1.0"},
                containers=[{"name": "web-container", "image": "nginx:1.21", "ready": True}],
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=5)
            ),
            PodInfo(
                cluster_name="test-cluster",
                namespace="kube-system",
                name="kube-dns-456",
                status="Running",
                phase="Running",
                node_name="master-node-1",
                labels={"app": "kube-dns", "k8s-app": "kube-dns"},
                containers=[{"name": "dns-container", "image": "k8s.gcr.io/coredns:1.8.6", "ready": True}],
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=5)
            )
        ]
        
        # 创建服务记录
        service_records = [
            ServiceInfo(
                cluster_name="test-cluster",
                namespace="default",
                name="web-service",
                type="ClusterIP",
                cluster_ip="***********",
                external_ip=None,
                ports=[{"port": 80, "targetPort": 8080, "protocol": "TCP"}],
                selector={"app": "web"},
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=5)
            ),
            ServiceInfo(
                cluster_name="test-cluster",
                namespace="kube-system",
                name="kube-dns",
                type="ClusterIP",
                cluster_ip="**********",
                external_ip=None,
                ports=[{"port": 53, "targetPort": 53, "protocol": "UDP"}],
                selector={"k8s-app": "kube-dns"},
                ttl_expires_at=datetime.utcnow() + timedelta(minutes=5)
            )
        ]
        
        # 4. 存储测试数据
        print("💾 存储测试数据到缓存...")
        
        # 存储集群
        cache_manager.create_record('clusters', cluster_record)
        print(f"   ✅ 集群: {cluster_record.name}")
        
        # 存储命名空间
        for ns in namespace_records:
            cache_manager.create_record('namespaces', ns)
            print(f"   ✅ 命名空间: {ns.name}")
        
        # 存储节点
        for node in node_records:
            cache_manager.create_record('nodes', node)
            print(f"   ✅ 节点: {node.name}")
        
        # 存储Pod
        for pod in pod_records:
            cache_manager.create_record('pods', pod)
            print(f"   ✅ Pod: {pod.namespace}/{pod.name}")
        
        # 存储服务
        for service in service_records:
            cache_manager.create_record('services', service)
            print(f"   ✅ 服务: {service.namespace}/{service.name}")
        
        print("✅ 所有测试数据存储完成")
        
        # 5. 验证数据存储
        print("\n🔍 验证数据存储...")
        
        # 查询各类资源
        clusters = cache_manager.list_records('clusters')
        namespaces = cache_manager.list_records('namespaces')
        nodes = cache_manager.list_records('nodes')
        pods = cache_manager.list_records('pods')
        services = cache_manager.list_records('services')
        
        print(f"📊 缓存统计:")
        print(f"   - 集群: {len(clusters)} 个")
        print(f"   - 命名空间: {len(namespaces)} 个")
        print(f"   - 节点: {len(nodes)} 个")
        print(f"   - Pod: {len(pods)} 个")
        print(f"   - 服务: {len(services)} 个")
        
        # 6. 详细显示数据
        print("\n📋 详细数据:")
        
        if clusters:
            cluster = clusters[0]
            print(f"🏢 集群信息:")
            print(f"   - 名称: {cluster.name}")
            print(f"   - 版本: {cluster.version}")
            print(f"   - 节点数: {cluster.node_count}")
            print(f"   - 状态: {cluster.status}")
        
        if namespaces:
            print(f"📁 命名空间:")
            for ns in namespaces:
                print(f"   - {ns.name} ({ns.status})")
        
        if nodes:
            print(f"🖥️ 节点:")
            for node in nodes:
                print(f"   - {node.name} ({node.status})")
        
        if pods:
            print(f"🐳 Pod:")
            for pod in pods:
                print(f"   - {pod.namespace}/{pod.name} ({pod.phase}) -> {pod.node_name}")
        
        if services:
            print(f"🌐 服务:")
            for service in services:
                print(f"   - {service.namespace}/{service.name} ({service.type}) -> {service.cluster_ip}")
        
        print("\n" + "=" * 60)
        print("✅ 缓存功能测试完成!")
        print("💡 扫描系统的缓存存储和查询功能正常工作")
        print("🔧 现在可以运行扫描器来填充真实的集群数据")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("🚀 启动缓存功能测试")
    asyncio.run(test_cache_functionality())
